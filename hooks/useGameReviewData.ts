import { useState, useEffect, useMemo } from 'react'
import { graphqlRequest } from '@/lib/api/graphql'
import { useUserContext } from '@/hooks/useUserContext'

// TypeScript interfaces for the GraphQL responses
export interface TimeSeriesNode {
  start_time: string
  end_time: string
  stats: {
    theme_counts: Array<{
      theme: string
      count: number
    }>
    average_move_length: number
    total_count?: number
    unique_game_count?: number
  }
}

export interface OpponentMistakesTimeSeries {
  nodes: TimeSeriesNode[]
}

export interface MyMistakesTimeSeries {
  nodes: TimeSeriesNode[]
}

export interface TagCount {
  tag: string
  count: number
}

export interface GameMoveBucket {
  name: string
  min_move: number
  max_move: number
  count: number
}

export interface UserColorCount {
  color: string
  count: number
}

export interface MoveLengthCount {
  length: number
  count: number
}

export interface OpponentMistakes3M {
  tag_counts: TagCount[]
  game_move_buckets: GameMoveBucket[]
  user_color_counts: UserColorCount[]
  move_length_counts: MoveLengthCount[]
}

export interface MyMistakes3M {
  tag_counts: TagCount[]
  game_move_buckets: GameMoveBucket[]
  user_color_counts: UserColorCount[]
  move_length_counts: MoveLengthCount[]
}

export interface GameReviewData {
  opponentMistakesTimeSeries?: OpponentMistakesTimeSeries
  opponentMistakesMissed3M?: OpponentMistakes3M
  opponentMistakesCaught3M?: OpponentMistakes3M
  opponentMistakesMissed3To6M?: OpponentMistakes3M
  opponentMistakesCaught3To6M?: OpponentMistakes3M
  myMistakesTimeSeries?: MyMistakesTimeSeries
  myMistakes3M?: MyMistakes3M
  // All-time data for Puzzle Complexity Trends
  opponentMistakesMissedAllTime?: { move_length_counts: MoveLengthCount[] }
  opponentMistakesCaughtAllTime?: { move_length_counts: MoveLengthCount[] }
  myMistakesAllTime?: { move_length_counts: MoveLengthCount[] }
  opponentMistakes?: any // Processed data
  myMistakes?: any // Processed data
  summary?: {
    recognitionRate: number
    mistakesPerGame: number
    totalGamesAnalyzed: number
    weeklyTrend: number
  }
}

interface UseGameReviewDataReturn {
  data: GameReviewData | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Consolidated GraphQL query for all game review data
const GAME_REVIEW_DATA_QUERY = `
  query GameReviewData($startTime6M: Time!, $startTime3M: Time!, $endTime: Time!) {
    # Time series data for opponent mistakes (6 months)
    opponentMistakesTimeSeries: myGroupedPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_CAUGHT, OPPONENT_BLUNDER_CAUGHT, OPPONENT_MISTAKE_MISSED, OPPONENT_BLUNDER_MISSED]
        game_start_time: $startTime6M
        game_end_time: $endTime
      }
      group_unit: WEEK
      group_length: 1
    ) {
      nodes {
        start_time
        end_time
        stats {
          theme_counts {
            theme
            count
          }
          average_move_length
        }
      }
    }

    # Time series data for my mistakes (6 months)
    myMistakesTimeSeries: myGroupedPuzzleStats(
      filter: {
        themes: [OWN_MISTAKE_PUNISHED, OWN_BLUNDER_PUNISHED, OWN_MISTAKE_ESCAPED, OWN_BLUNDER_ESCAPED]
        game_start_time: $startTime6M
        game_end_time: $endTime
      }
      group_unit: WEEK
      group_length: 1
    ) {
      nodes {
        start_time
        end_time
        stats {
          total_count
          unique_game_count
          average_move_length
        }
      }
    }

    # Recent 3 months - Opponent mistakes missed
    opponentMistakesMissed3M: myPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_MISSED, OPPONENT_BLUNDER_MISSED]
        game_start_time: $startTime3M
        game_end_time: $endTime
      }
    ) {
      tag_counts {
        tag
        count
      }
      game_move_buckets {
        name
        min_move
        max_move
        count
      }
      user_color_counts {
        color
        count
      }
      move_length_counts {
        length
        count
      }
    }

    # Recent 3 months - Opponent mistakes caught
    opponentMistakesCaught3M: myPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_CAUGHT, OPPONENT_BLUNDER_CAUGHT]
        game_start_time: $startTime3M
        game_end_time: $endTime
      }
    ) {
      tag_counts {
        tag
        count
      }
      game_move_buckets {
        name
        min_move
        max_move
        count
      }
      user_color_counts {
        color
        count
      }
      move_length_counts {
        length
        count
      }
    }

    # Previous 3-6 months - Opponent mistakes missed
    opponentMistakesMissed3To6M: myPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_MISSED, OPPONENT_BLUNDER_MISSED]
        game_start_time: $startTime6M
        game_end_time: $startTime3M
      }
    ) {
      tag_counts {
        tag
        count
      }
      game_move_buckets {
        name
        min_move
        max_move
        count
      }
      user_color_counts {
        color
        count
      }
      move_length_counts {
        length
        count
      }
    }

    # Previous 3-6 months - Opponent mistakes caught
    opponentMistakesCaught3To6M: myPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_CAUGHT, OPPONENT_BLUNDER_CAUGHT]
        game_start_time: $startTime6M
        game_end_time: $startTime3M
      }
    ) {
      tag_counts {
        tag
        count
      }
      game_move_buckets {
        name
        min_move
        max_move
        count
      }
      user_color_counts {
        color
        count
      }
      move_length_counts {
        length
        count
      }
    }

    # My mistakes (recent 3 months)
    myMistakes3M: myPuzzleStats(
      filter: {
        themes: [OWN_MISTAKE_PUNISHED, OWN_BLUNDER_PUNISHED, OWN_MISTAKE_ESCAPED, OWN_BLUNDER_ESCAPED]
        game_start_time: $startTime3M
        game_end_time: $endTime
      }
    ) {
      tag_counts {
        tag
        count
      }
      game_move_buckets {
        name
        min_move
        max_move
        count
      }
      user_color_counts {
        color
        count
      }
      move_length_counts {
        length
        count
      }
    }

    # All-time data for Puzzle Complexity Trends (no time filters)
    opponentMistakesMissedAllTime: myPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_MISSED, OPPONENT_BLUNDER_MISSED]
      }
    ) {
      move_length_counts {
        length
        count
      }
    }

    opponentMistakesCaughtAllTime: myPuzzleStats(
      filter: {
        themes: [OPPONENT_MISTAKE_CAUGHT, OPPONENT_BLUNDER_CAUGHT]
      }
    ) {
      move_length_counts {
        length
        count
      }
    }

    myMistakesAllTime: myPuzzleStats(
      filter: {
        themes: [OWN_MISTAKE_PUNISHED, OWN_BLUNDER_PUNISHED, OWN_MISTAKE_ESCAPED, OWN_BLUNDER_ESCAPED]
      }
    ) {
      move_length_counts {
        length
        count
      }
    }
  }
`

export function useGameReviewData(): UseGameReviewDataReturn {
  const { user } = useUserContext()
  const [data, setData] = useState<GameReviewData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Calculate date ranges
  const endDate = new Date()
  const startDate3M = new Date()
  startDate3M.setMonth(startDate3M.getMonth() - 3) // 3 months ago
  const startDate6M = new Date()
  startDate6M.setMonth(startDate6M.getMonth() - 6) // 6 months ago

  const endTime = endDate.toISOString()
  const startTime3M = startDate3M.toISOString()
  const startTime6M = startDate6M.toISOString()



  const fetchData = async () => {
    // Profiles are removed for MVP. We won't fetch data.
    setError('Game Review is disabled for MVP')
    return

    try {
      // Single consolidated GraphQL request
      const result = await graphqlRequest(GAME_REVIEW_DATA_QUERY, {
        startTime6M,
        startTime3M,
        endTime
      })

      // Process results - all data comes from single query
      const gameReviewData: GameReviewData = {
        opponentMistakesTimeSeries: result.opponentMistakesTimeSeries,
        opponentMistakesMissed3M: result.opponentMistakesMissed3M,
        opponentMistakesCaught3M: result.opponentMistakesCaught3M,
        opponentMistakesMissed3To6M: result.opponentMistakesMissed3To6M,
        opponentMistakesCaught3To6M: result.opponentMistakesCaught3To6M,
        myMistakesTimeSeries: result.myMistakesTimeSeries,
        myMistakes3M: result.myMistakes3M,
        // All-time data for Puzzle Complexity Trends
        opponentMistakesMissedAllTime: result.opponentMistakesMissedAllTime,
        opponentMistakesCaughtAllTime: result.opponentMistakesCaughtAllTime,
        myMistakesAllTime: result.myMistakesAllTime
      }

      // Process and calculate summary metrics
      gameReviewData.summary = calculateSummaryMetrics(gameReviewData)

      setData(gameReviewData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch game review data')
    } finally {
      setIsLoading(false)
    }
  }

  // Auto-fetch when user has profiles
  useEffect(() => {
    // Game Review disabled in MVP; do not auto-fetch
  }, [])

  return {
    data,
    isLoading,
    error,
    refetch: fetchData
  }
}

// Helper function to calculate summary metrics
function calculateSummaryMetrics(data: GameReviewData) {
  const summary = {
    recognitionRate: 0,
    mistakesPerGame: 0,
    totalGamesAnalyzed: 0,
    weeklyTrend: 0
  }

  // Calculate recognition rate from time series data
  if (data.opponentMistakesTimeSeries?.nodes) {
    let totalCaught = 0
    let totalMissed = 0

    data.opponentMistakesTimeSeries.nodes.forEach(node => {
      node.stats.theme_counts.forEach(theme => {
        if (theme.theme.includes('caught')) {
          totalCaught += theme.count
        } else if (theme.theme.includes('missed')) {
          totalMissed += theme.count
        }
      })
    })

    if (totalCaught + totalMissed > 0) {
      summary.recognitionRate = (totalCaught / (totalCaught + totalMissed)) * 100
    }
  }

  // Calculate mistakes per game from time series data
  if (data.myMistakesTimeSeries?.nodes) {
    let totalMistakes = 0
    let totalGames = 0

    data.myMistakesTimeSeries.nodes.forEach(node => {
      totalMistakes += node.stats.total_count || 0
      totalGames += node.stats.unique_game_count || 0
    })

    if (totalGames > 0) {
      summary.mistakesPerGame = totalMistakes / totalGames
      summary.totalGamesAnalyzed = totalGames
    }
  }

  // Calculate weekly trend (simplified - comparing last 2 weeks)
  if (data.opponentMistakesTimeSeries?.nodes && data.opponentMistakesTimeSeries.nodes.length >= 2) {
    const recentWeeks = data.opponentMistakesTimeSeries.nodes.slice(-2)
    const rates = recentWeeks.map(week => {
      let caught = 0
      let missed = 0
      week.stats.theme_counts.forEach(theme => {
        if (theme.theme.includes('caught')) caught += theme.count
        else if (theme.theme.includes('missed')) missed += theme.count
      })
      return caught + missed > 0 ? (caught / (caught + missed)) * 100 : 0
    })
    
    if (rates.length === 2) {
      summary.weeklyTrend = rates[1] - rates[0]
    }
  }

  return summary
}
