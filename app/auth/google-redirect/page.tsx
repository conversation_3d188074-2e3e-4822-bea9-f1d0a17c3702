"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { useAuthContext } from "@/components/auth/auth-provider"
import { APP_CONFIG } from "@/lib/config"

export default function GoogleRedirectPage() {
  const router = useRouter()
  const { checkAuth } = useAuthContext()

  useEffect(() => {
    // Handle the redirect result
    const handleRedirect = async () => {
      try {
        // Force auth check which will handle redirect result
        await checkAuth()
        
        // Small delay to ensure state is updated
        setTimeout(() => {
          router.push(APP_CONFIG.DEFAULT_LOGIN_REDIRECT)
        }, 100)
      } catch (error) {
        console.error("Error handling Google redirect:", error)
        router.push("/auth?error=redirect_failed")
      }
    }

    handleRedirect()
  }, [checkAuth, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardContent className="p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Completing Google sign-in...</p>
            <p className="mt-2 text-sm text-gray-500">Please wait while we authenticate you</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}