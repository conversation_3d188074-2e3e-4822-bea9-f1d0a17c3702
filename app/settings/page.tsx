"use client"

import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"
import { MobileLayout, MobileContainer, MobileCard } from "@/components/ui/mobile-layout"
import { Settings as SettingsIcon } from "lucide-react"
import { useAuthContext } from "@/components/auth/auth-provider"

export default function SettingsPage() {
  const { logout } = useAuthContext()

  return (
    <MobileLayout fullHeight>
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Navigation />

        <MobileContainer padding="lg">
          <MobileCard padding="lg" className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
              <div>
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 flex items-center">
                  <SettingsIcon className="h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8 mr-2 sm:mr-3 text-blue-600" />
                  Settings
                </h1>
                <p className="text-base sm:text-lg lg:text-xl text-gray-600">Manage your account</p>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  onClick={logout}
                  variant="outline"
                  className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400 text-sm sm:text-base"
                >
                  Sign Out
                </Button>
              </div>
            </div>
          </MobileCard>
        </MobileContainer>

        <Footer hideOnMobile={true} />
      </div>
    </MobileLayout>
  )
}
