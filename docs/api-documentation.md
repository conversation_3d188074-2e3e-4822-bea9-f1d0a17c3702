# API Documentation

This document lists all available API endpoints for the Chessticize Server, including HTTP methods, URL paths, required headers, authentication requirements, path parameters, query parameters, and request body fields.

## Key Features

- **Sprint System**: Timed puzzle-solving sessions with performance tracking
- **Puzzle Queue**: Spaced repetition system for practicing specific puzzle types
- **Learning Queue**: Automatic spaced repetition for failed sprint puzzles (2→4→7→15 day intervals)
- **Arrow-Duel Mode**: Enhanced puzzle practice with candidate move selection
- **Daily Statistics**: Comprehensive tracking of learning progress and performance

---

## Health Check
**GET** `/health`

- Authentication: None
- Headers: None
- Query Parameters: None
- Request Body: None

---

## Authentication

### Session Token Behavior

Session tokens provide long-term authentication (default: 30 days) and are automatically generated when logging in with email/password. Key behaviors:

- **Security**: Session tokens are hashed before storage (like passwords) and cannot be retrieved in plain text
- **Auto-extension**: Using a session token for login automatically extends its expiration
- **Single generation**: Session tokens are only created for email/password login, not when authenticating with an existing session token
- **User-Agent tracking**: Each session token records the User-Agent string for identification

### Authentication

Chessticize uses Firebase Authentication for all sign-in/sign-up flows (Email/Password, Magic Link, Google). Clients authenticate with Firebase directly, then exchange the Firebase ID token using the Firebase Token Exchange endpoint below.
### Admin Register (Admin Only)
**POST** `/api/v1/auth/admin/register`

- Authentication: Bearer token with Admin privileges
- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field    | Type   | Required | Description                         |
  | -------- | ------ | -------- | ----------------------------------- |
  | email    | string | yes      | New user's email                    |
  | password | string | yes      | New user's password (6–40 chars)    |

- Query Parameters: None

### Firebase Token Exchange
**POST** `/api/v1/auth/firebase-exchange`

- Authentication: None (Firebase token provides authentication)
- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field           | Type   | Required | Description                                |
  | --------------- | ------ | -------- | ------------------------------------------ |
  | firebase_token  | string | yes      | Firebase ID token from client authentication |

- Response (200 OK):
  ```json
  {
    "token": "eyJhbGciOiJIUzI1NiIsInR5...",
    "session_token": "abcdefghijklmnopqrstuvwxyz123456789ABCDEFG",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "is_new_user": false
  }
  ```

- Error Responses:
  - `400 Bad Request`: Invalid or missing firebase_token
  - `401 Unauthorized`: Invalid Firebase token
  - `500 Internal Server Error`: Server error during token exchange


### Firebase Token Exchange
**POST** `/api/v1/auth/firebase-exchange`

- Authentication: None
- Headers: None
- Query Parameters: None
- Request Body:
  - `firebase_token` (string, required): Firebase ID token to exchange for our JWT token

**Important**: This endpoint only accepts Firebase tokens from email link sign-in. Password-based sign-in tokens will be rejected with a 401 Unauthorized error.

**Response** (201 Created for new users, 200 OK for existing users):
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5...",
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "firebase_uid": "firebase-user-123"
  }
}
```

**Note**: This endpoint does not return session tokens. Firebase provides its own refresh tokens for token renewal.

**Error Response** (401 Unauthorized for password sign-in):
```json
{
  "error": "Password sign-in is not allowed. Please use email link sign-in."
}
```

---

## User Endpoints (Authenticated)
All endpoints below require the header:
```
Authorization: Bearer <access_token>
```

### Get Current User
**GET** `/api/v1/users/me`

- Path Parameters: None
- Query Parameters:
  | Parameter   | Type   | Required | Description                                          |
  | ----------- | ------ | -------- | ---------------------------------------------------- |
  | days        | int    | no       | Number of days to include in daily stats (default: 7, max: 365) |
  | start_date  | string | no       | Start date for daily stats in YYYY-MM-DD format     |
  | end_date    | string | no       | End date for daily stats in YYYY-MM-DD format       |
- Request Body: None

### List My Chess Profiles
**GET** `/api/v1/users/me/chess-profiles`

- Path Parameters: None
- Query Parameters: None
- Request Body: None

### Create My Chess Profile
**POST** `/api/v1/users/me/chess-profiles`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field    | Type   | Required | Description                       |
  | -------- | ------ | -------- | --------------------------------- |
  | platform | string | yes      | Chess platform (e.g., lichess.org)|
  | username | string | yes      | Chess account username            |

- Query Parameters: None

### Delete My Chess Profile
**DELETE** `/api/v1/users/me/chess-profiles/{profileID}`

- Path Parameters:
  - `profileID` (string, required): ID of the chess profile
- Query Parameters: None
- Request Body: None

### List My Games
**GET** `/api/v1/users/me/games`

- Path Parameters: None
- Query Parameters:
  | Parameter   | Type   | Required | Description                                          |
  | ----------- | ------ | -------- | ---------------------------------------------------- |
  | offset      | int    | no       | Pagination offset (default: 0)                       |
  | limit       | int    | no       | Pagination limit (1–100, default: 50)                |
  | platform    | string | no       | Filter by `CHESS_COM` or `LICHESS`                   |
  | username    | string | no       | Filter by chess username                             |
  | start_time  | RFC3339| no       | Filter games from this timestamp                     |
  | end_time    | RFC3339| no       | Filter games up to this timestamp                    |
- Request Body: None

### List My Puzzles
**GET** `/api/v1/users/me/puzzles`

- Path Parameters: None
- Query Parameters:
  | Parameter         | Type     | Required | Description                                    |
  | ----------------- | -------- | -------- | ---------------------------------------------- |
  | offset            | int      | no       | Pagination offset (default: 0)                 |
  | limit             | int      | no       | Pagination limit (1–100, default: 50)          |
  | tags              | string   | no       | Comma-separated tags filter                    |
  | game_start_time   | RFC3339  | no       | Filter puzzles from game start timestamp       |
  | game_end_time     | RFC3339  | no       | Filter puzzles up to game end timestamp        |
- Request Body: None

### Submit Puzzle Attempt
**POST** `/api/v1/users/me/puzzles/{puzzleID}/attempts`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `puzzleID` (string, required): ID of the puzzle
- Request Body (JSON):

  | Field           | Type     | Required | Description                                    |
  | --------------- | -------- | -------- | ---------------------------------------------- |
  | attempt_type    | string   | no       | Type of attempt: "regular" or "arrow_duel" (defaults to "regular") |
  | solved          | boolean  | yes      | Whether the puzzle was solved correctly        |
  | time_spent      | int      | yes      | Time spent in seconds                          |
  | moves           | []string | yes      | List of moves made by the user                 |
  | is_disliked     | boolean  | no       | Whether the user dislikes this puzzle          |
  | candidate_moves | []string | no*      | Array of exactly 2 moves [blunder, correct] (required for arrow_duel) |
  | chosen_move     | string   | no*      | Move chosen by the player (required for arrow_duel) |

  *Required only when `attempt_type` is "arrow_duel"

- Query Parameters: None

**Regular Attempt Example:**
```json
{
  "solved": true,
  "time_spent": 30,
  "moves": ["e2e4", "e7e5"],
  "is_disliked": false
}
```

**Arrow-Duel Attempt Example:**
```json
{
  "attempt_type": "arrow_duel",
  "solved": true,
  "time_spent": 25,
  "moves": ["Nxe5"],
  "candidate_moves": ["Nxd4", "Nxe5"],
  "chosen_move": "Nxe5",
  "is_disliked": false
}
```

### Submit Lichess Puzzle Attempt
**POST** `/api/v1/users/me/lichess-puzzles/{puzzleID}/attempts`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `puzzleID` (string, required): ID of the Lichess puzzle
- Request Body (JSON):

  | Field               | Type     | Required | Description                                    |
  | ------------------- | -------- | -------- | ---------------------------------------------- |
  | attempt_type        | string   | no       | Type of attempt: "regular" or "arrow_duel" (defaults to "regular") |
  | solved              | boolean  | yes      | Whether the puzzle was solved correctly        |
  | time_spent          | int      | yes      | Time spent in seconds                          |
  | moves               | []string | yes      | List of moves made by the user                 |
  | is_disliked         | boolean  | no       | Whether the user dislikes this puzzle          |
  | is_learning_attempt | boolean  | no       | Whether this is a learning queue retry attempt (enables spaced repetition) |
  | candidate_moves     | []string | no*      | Array of exactly 2 moves [blunder, correct] (required for arrow_duel) |
  | chosen_move         | string   | no*      | Move chosen by the player (required for arrow_duel) |

  *Required only when `attempt_type` is "arrow_duel"

- Query Parameters: None

**Regular Attempt Example:**
```json
{
  "solved": false,
  "time_spent": 45,
  "moves": ["Qh4"],
  "is_disliked": true
}
```

**Arrow-Duel Attempt Example:**
```json
{
  "attempt_type": "arrow_duel",
  "solved": false,
  "time_spent": 20,
  "moves": ["Qh4"],
  "candidate_moves": ["Qh4", "Qh5"],
  "chosen_move": "Qh4",
  "is_disliked": false
}
```

**Learning Queue Retry Example:**
```json
{
  "solved": true,
  "time_spent": 35,
  "moves": ["Qh5"],
  "is_learning_attempt": true
}
```

**Note**: When `is_learning_attempt` is true, the system will update the learning queue with spaced repetition scheduling. Correct attempts advance the puzzle through the spaced repetition intervals (2→4→7→15 days), while incorrect attempts reset the progression. After 5 consecutive correct attempts, the puzzle is considered mastered and removed from the learning queue.

---

## Arrow-Duel Feature

Arrow-Duel is a special puzzle mode where players choose between two candidate moves: a blunder move and the correct move. This feature provides a different type of puzzle-solving experience focused on move evaluation rather than calculation.

### Key Concepts

- **Attempt Type**: Set to "arrow_duel" to enable arrow-duel mode
- **Candidate Moves**: Exactly 2 moves must be provided - [blunder_move, correct_move]
- **Chosen Move**: The move selected by the player from the candidate moves
- **Separate Stats**: Arrow-duel attempts create separate statistics from regular attempts
- **Separate ELO**: Arrow-duel uses separate ELO ratings (e.g., "arrowduel 5/30")

### Arrow-Duel ELO Types

Arrow-duel ELO types follow the format: `arrowduel {duration}/{per_puzzle_time}`

**Valid Examples:**
- `arrowduel 5/30` - 5 minutes total, 30 seconds per puzzle
- `arrowduel 10/20` - 10 minutes total, 20 seconds per puzzle
- `arrowduel 15/60` - 15 minutes total, 60 seconds per puzzle

**Valid Durations:** 1-30 minutes
**Valid Per-Puzzle Times:** 5, 10, 15, 20, 30, 60 seconds

### Validation Rules

For arrow-duel attempts:
1. `attempt_type` must be "arrow_duel"
2. `candidate_moves` must contain exactly 2 moves
3. `chosen_move` must be one of the candidate moves
4. All regular puzzle fields are still required (`solved`, `time_spent`, `moves`)

### Backward Compatibility

- Missing `attempt_type` defaults to "regular"
- Arrow-duel fields are ignored for regular attempts
- Existing APIs continue to work without changes

---

## Sprint Endpoints (Authenticated)

All sprint endpoints require the header:
```
Authorization: Bearer <access_token>
```

### Start Sprint
**POST** `/api/v1/users/me/sprint/start`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field    | Type   | Required | Description                           |
  | -------- | ------ | -------- | ------------------------------------- |
  | elo_type | string | yes      | ELO type for the sprint               |

  **ELO Type Format**:
  - **Regular sprints**: `"<theme> <target>/<time>"` (e.g., `"fork 10/30"`, `"tactics 5/15"`)
  - **Arrow-duel sprints**: `"arrowduel <theme> <target>/<time>"` (e.g., `"arrowduel fork 10/30"`, `"arrowduel tactics 5/15"`)

  **Arrow-Duel Sprints**:
  - Puzzles are filtered using Stockfish evaluations to ensure they're suitable for arrow-duel gameplay
  - Puzzle responses include additional evaluation data (`best_move_eval`, `best_move`, `position_eval_after_first_move`)
  - Results must include `candidate_moves` and `chosen_move` fields

- Query Parameters: None

### Get Sprint State
**GET** `/api/v1/users/me/sprint/{sessionId}`

- Path Parameters:
  - `sessionId` (string, required): Sprint session ID
- Query Parameters: None
- Request Body: None

### End Sprint
**POST** `/api/v1/users/me/sprint/{sessionId}/end`

- Headers:
  - `Content-Type: application/json` (optional, only if providing request body)
- Path Parameters:
  - `sessionId` (string, required): Sprint session ID
- Query Parameters: None
- Request Body (JSON, optional): Client can provide final results to override server calculations

  | Field           | Type | Required | Description                                    |
  | --------------- | ---- | -------- | ---------------------------------------------- |
  | puzzles_solved  | int  | no       | Client's count of puzzles solved (overrides server count) |
  | mistakes_made   | int  | no       | Client's count of mistakes made (overrides server count)  |

  **Note**: This feature helps mitigate issues where puzzle results might be missed or duplicated due to network issues. The client has the final say on sprint results.

**Learning Queue Integration**: When a sprint ends, any failed puzzles are automatically added to the user's learning queue for spaced repetition practice. This enables users to systematically improve on their mistakes through the learning queue system.

### Get Next Puzzles for Sprint
**POST** `/api/v1/users/me/sprint/{sessionId}/next-puzzles`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `sessionId` (string, required): Sprint session ID
- Request Body (JSON):

  | Field | Type | Required | Description                                    |
  | ----- | ---- | -------- | ---------------------------------------------- |
  | count | int  | no       | Number of puzzles to generate (1–50, default: 10) |

- Query Parameters: None

### Submit Sprint Puzzle Results
**POST** `/api/v1/users/me/sprint/{sessionId}/results`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `sessionId` (string, required): Sprint session ID
- Request Body (JSON):

  | Field   | Type                      | Required | Description                    |
  | ------- | ------------------------- | -------- | ------------------------------ |
  | results | []PuzzleAttemptRequest    | yes      | Array of puzzle attempt results |

  **PuzzleAttemptRequest Object:**
  | Field             | Type      | Required | Description                           |
  | ----------------- | --------- | -------- | ------------------------------------- |
  | puzzle_id         | string    | yes      | ID of the puzzle                      |
  | sequence_in_sprint| int       | yes      | Sequence number in the sprint         |
  | user_moves        | []string  | yes      | List of moves made by the user        |
  | was_correct       | boolean   | yes      | Whether the attempt was correct       |
  | time_taken_ms     | int       | yes      | Time taken in milliseconds           |
  | attempted_at      | RFC3339   | yes      | Timestamp when attempt was made       |
  | attempt_type      | string    | no       | Type of attempt: "regular" or "arrow_duel" (defaults to "regular") |
  | candidate_moves   | []string  | no*      | Array of exactly 2 moves [blunder_move, correct_move] (required for arrow_duel) |
  | chosen_move       | string    | no*      | Move chosen by the player (required for arrow_duel) |

  *Required only when `attempt_type` is "arrow_duel"

- Query Parameters: None

**Deduplication Behavior**:
- The API automatically prevents duplicate submissions for the same puzzle within a sprint
- If a puzzle result is submitted multiple times, only the first submission is processed
- Subsequent submissions for the same puzzle are silently ignored (not counted in `processed_count`)
- This helps prevent double-counting when clients retry failed requests or have network issues

**Regular Sprint Attempt Example:**
```json
{
  "results": [
    {
      "puzzle_id": "00123",
      "sequence_in_sprint": 1,
      "user_moves": ["e2e4", "e7e5"],
      "was_correct": true,
      "time_taken_ms": 15000,
      "attempted_at": "2023-10-01T12:00:00Z"
    }
  ]
}
```

**Arrow-Duel Sprint Attempt Example:**
```json
{
  "results": [
    {
      "puzzle_id": "00124",
      "sequence_in_sprint": 2,
      "user_moves": ["Qh4"],
      "was_correct": false,
      "time_taken_ms": 8000,
      "attempted_at": "2023-10-01T12:01:00Z",
      "attempt_type": "arrow_duel",
      "candidate_moves": ["Qh4", "Qh5"],
      "chosen_move": "Qh4"
    }
  ]
}
```

**Arrow-Duel Validation Rules:**
1. `attempt_type` must be "arrow_duel"
2. `candidate_moves` must contain exactly 2 moves
3. `chosen_move` must be one of the candidate moves

### Get Sprint Puzzles
**GET** `/api/v1/users/me/sprint/{sessionId}/puzzles`

- Path Parameters:
  - `sessionId` (string, required): Sprint session ID
- Query Parameters:
  | Parameter     | Type   | Required | Description                                    |
  | ------------- | ------ | -------- | ---------------------------------------------- |
  | offset        | int    | no       | Pagination offset (default: 0)                 |
  | limit         | int    | no       | Pagination limit (1–100, default: 50)          |
  | status        | string | no       | Filter by attempt status: `unattempted`, `solved`, `failed`, `attempted` |
  | sequence_min  | int    | no       | Minimum sequence number in sprint              |
  | sequence_max  | int    | no       | Maximum sequence number in sprint              |
  | attempt_type  | string | no       | Filter by attempt type: `regular`, `arrow_duel` |
- Request Body: None

**Response Body (JSON):**
```json
{
  "puzzles": [
    {
      "puzzle_id": "00123",
      "sequence_in_sprint": 5,
      "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "solution_moves": ["e2e4", "e7e5"],
      "rating": 1500,
      "themes": ["tactics", "fork"],
      "attempt_status": "failed",
      "user_moves": ["d2d4", "e7e6"],
      "was_correct": false,
      "time_taken_ms": 15000,
      "attempted_at": "2023-10-01T12:05:30Z",
      "attempt_type": "arrow_duel",
      "candidate_moves": ["d2d4", "e2e4"],
      "chosen_move": "d2d4"
    }
  ],
  "total_count": 3,
  "offset": 0,
  "limit": 50
}
```

**Response Fields (per puzzle object):**
| Field             | Type     | Always Present | Description                                    |
| ----------------- | -------- | -------------- | ---------------------------------------------- |
| puzzle_id         | string   | yes            | ID of the puzzle                               |
| sequence_in_sprint| int      | yes            | Sequence number in the sprint                  |
| fen               | string   | yes            | FEN notation of the puzzle position            |
| solution_moves    | []string | yes            | Correct solution moves                         |
| rating            | int      | yes            | Puzzle difficulty rating                       |
| themes            | []string | yes            | Puzzle themes/categories                       |
| attempt_status    | string   | yes            | Status: "unattempted", "solved", "failed"     |
| user_moves        | []string | no             | Moves made by user (only if attempted)         |
| was_correct       | boolean  | no             | Whether attempt was correct (only if attempted)|
| time_taken_ms     | int      | no             | Time taken in milliseconds (only if attempted)|
| attempted_at      | string   | no             | RFC3339 timestamp (only if attempted)          |
| attempt_type      | string   | no             | "regular" or "arrow_duel" (only if attempted)  |
| candidate_moves   | []string | no             | [blunder, correct] moves (only for arrow_duel) |
| chosen_move       | string   | no             | Player's chosen move (only for arrow_duel)     |

**Status Filter Values:**
- `unattempted`: Puzzles that haven't been attempted yet
- `solved`: Puzzles that were solved correctly
- `failed`: Puzzles that were attempted but failed
- `attempted`: All attempted puzzles (both solved and failed)

**Example Usage:**
```bash
# Get all failed puzzles from a sprint
GET /api/v1/users/me/sprint/{sessionId}/puzzles?status=failed

# Get all arrow-duel attempts for review
GET /api/v1/users/me/sprint/{sessionId}/puzzles?attempt_type=arrow_duel

# Get failed arrow-duel attempts specifically
GET /api/v1/users/me/sprint/{sessionId}/puzzles?status=failed&attempt_type=arrow_duel

# Get first 10 puzzles (regardless of status)
GET /api/v1/users/me/sprint/{sessionId}/puzzles?limit=10

# Get puzzles 5-15 that were failed
GET /api/v1/users/me/sprint/{sessionId}/puzzles?status=failed&sequence_min=5&sequence_max=15

# Get all attempted puzzles (both solved and failed)
GET /api/v1/users/me/sprint/{sessionId}/puzzles?status=attempted
```

### List My Events
**GET** `/api/v1/users/me/events`

- Path Parameters: None
- Query Parameters:
  | Parameter    | Type     | Required | Description                                    |
  | ------------ | -------- | -------- | ---------------------------------------------- |
  | offset       | int      | no       | Pagination offset (default: 0)                 |
  | limit        | int      | no       | Pagination limit (1–100, default: 50)          |
  | event_types  | string   | no       | Event type filter                              |
  | start_time   | RFC3339  | no       | Filter events from this timestamp             |
  | end_time     | RFC3339  | no       | Filter events up to this timestamp            |
- Request Body: None

---

## Random Puzzle Endpoints (Authenticated)

All random puzzle endpoints require the header:
```
Authorization: Bearer <access_token>
```

### Get Random Lichess Puzzles
**POST** `/api/v1/users/me/random-puzzles/lichess`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field     | Type     | Required | Description                                    |
  | --------- | -------- | -------- | ---------------------------------------------- |
  | min_rating| int      | yes      | Minimum puzzle rating (0–3000)                |
  | max_rating| int      | yes      | Maximum puzzle rating (0–3000)                |
  | themes    | []string | no       | Array of theme filters                         |
  | limit     | int      | yes      | Number of puzzles to return (1–50)            |

- Query Parameters: None

### Get Random User Puzzles
**POST** `/api/v1/users/me/random-puzzles/user`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field           | Type      | Required | Description                                    |
  | --------------- | --------- | -------- | ---------------------------------------------- |
  | min_move_length | int       | yes      | Minimum move length (2–20, even numbers only) |
  | max_move_length | int       | yes      | Maximum move length (2–20, even numbers only) |
  | tags            | []string  | no       | Array of tag filters                           |
  | themes          | []string  | no       | Array of theme filters                         |
  | game_time_start | RFC3339   | no       | Filter puzzles from game start timestamp      |
  | game_time_end   | RFC3339   | no       | Filter puzzles up to game end timestamp       |
  | limit           | int       | yes      | Number of puzzles to return (1–50)            |

- Query Parameters: None

---

## Puzzle Queue Endpoints (Authenticated)

All puzzle queue endpoints require the header:
```
Authorization: Bearer <access_token>
```

### Add Puzzles to Queue
**POST** `/api/v1/users/me/puzzle-queue/add`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field      | Type   | Required | Description                                    |
  | ---------- | ------ | -------- | ---------------------------------------------- |
  | count      | int    | yes      | Number of puzzles to add (1–200)              |
  | mistake_by | string | no       | Filter by mistake type: "opponent" or "own"   |

- Query Parameters: None

### Get Due Puzzles
**GET** `/api/v1/users/me/puzzle-queue/due`

- Path Parameters: None
- Query Parameters:
  | Parameter  | Type   | Required | Description                                    |
  | ---------- | ------ | -------- | ---------------------------------------------- |
  | limit      | int    | no       | Number of puzzles to return (1–50, default: 10)|
  | mistake_by | string | no       | Filter by mistake type: "opponent" or "own"   |
- Request Body: None

### Get Queue Statistics
**GET** `/api/v1/users/me/puzzle-queue/stats`

- Path Parameters: None
- Query Parameters: None
- Request Body: None

---

## Learning Queue Endpoints (Authenticated)

All learning queue endpoints require the header:
```
Authorization: Bearer <access_token>
```

The learning queue system implements spaced repetition for failed puzzles from sprints. Puzzles are automatically added to the learning queue when they fail during sprints, and users can practice them with optimal spacing intervals (2→4→7→15 days) until mastery (5 consecutive correct attempts).

### Get Due Learning Puzzles
**GET** `/api/v1/users/me/learning-queue/due`

- Path Parameters: None
- Query Parameters:
  | Parameter    | Type   | Required | Description                                    |
  | ------------ | ------ | -------- | ---------------------------------------------- |
  | limit        | int    | no       | Number of puzzles to return (1–50, default: 10)|
  | attempt_type | string | no       | Filter by attempt type: "regular" or "arrow_duel" |
- Request Body: None

### Get Learning Queue Statistics
**GET** `/api/v1/users/me/learning-queue/stats`

- Path Parameters: None
- Query Parameters: None
- Request Body: None

---

## Admin Endpoints (Requires Admin Token)
All endpoints below require the header:
```
Authorization: Bearer <admin_token>
```

### Users Management

#### List Users
**GET** `/api/v1/admin/users`

- Path Parameters: None
- Query Parameters: None
- Request Body: None

#### Create User
**POST** `/api/v1/admin/users`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field    | Type   | Required | Description      |
  | -------- | ------ | -------- | ---------------- |
  | email    | string | yes      | User email       |
  | password | string | yes      | User password    |

- Query Parameters: None

#### Get User By ID
**GET** `/api/v1/admin/users/{id}`

- Path Parameters:
  - `id` (string, required): User ID
- Query Parameters: None
- Request Body: None

#### Update User
**PUT** `/api/v1/admin/users/{id}`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `id` (string, required): User ID
- Request Body (JSON):

  | Field    | Type    | Required | Description                |
  | -------- | ------- | -------- | -------------------------- |
  | email    | string  | no       | Updated user email         |
  | password | string  | no       | Updated password           |

- Query Parameters: None

#### List User's Chess Profiles
**GET** `/api/v1/admin/users/{id}/chess-profiles`

- Path Parameters:
  - `id` (string, required): User ID
- Query Parameters: None
- Request Body: None

#### Refresh Chess Profile
**PUT** `/api/v1/admin/users/{id}/chess-profiles/{profileID}/refresh`

- Path Parameters:
  - `id` (string, required): User ID
  - `profileID` (string, required): Chess profile ID
- Query Parameters: None
- Request Body: None

### Games Management

#### Create Game
**POST** `/api/v1/admin/games`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON): fields matching `CreateGameRequest`:

  | Field             | Type                | Required | Description              |
  | ----------------- | ------------------- | -------- | ------------------------ |
  | user_id           | string              | yes      | Owning user ID           |
  | platform          | string              | yes      | Chess platform enum      |
  | chess_username    | string              | yes      | Chess account username   |
  | user_color        | string              | yes      | Color enum (White/Black) |
  | game_time         | RFC3339 timestamp   | yes      | Game played timestamp    |
  | pgn               | string              | yes      | PGN data      |
  | time_control      | string              | yes      | Time control string      |
  | rated             | boolean             | yes      | Rated game flag          |
  | url               | string (optional)   | no       | Game URL                 |
  | white_player      | string              | yes      | JSON-serialized PlayerInfo for White |
  | black_player      | string              | yes      | JSON-serialized PlayerInfo for Black |
  | winner            | string              | yes      | Winner enum (WHITE, BLACK, NONE)              |
  | result            | string              | yes      | Game result enum (MATE, RESIGN, DRAW, ABANDONED, OUT_OF_TIME)         |

- Query Parameters: None

#### List Games By User
**GET** `/api/v1/admin/games/user/{userId}`

- Path Parameters:
  - `userId` (string, required): User ID
- Query Parameters:
  | Parameter   | Type     | Required | Description                                          |
  | ----------- | -------- | -------- | ---------------------------------------------------- |
  | offset      | int      | no       | Pagination offset (default: 0)                       |
  | limit       | int      | no       | Pagination limit (1–100, default: 50)                |
  | start_time  | RFC3339  | no       | Filter games from this timestamp                     |
  | end_time    | RFC3339  | no       | Filter games up to this timestamp                    |
- Request Body: None

#### Delete Game
**DELETE** `/api/v1/admin/games/{id}`

- Path Parameters:
  - `id` (string, required): Game ID
- Query Parameters: None
- Request Body: None

#### Get Game By ID
**GET** `/api/v1/admin/games/{id}`

- Path Parameters:
  - `id` (string, required): Game ID
- Query Parameters: None
- Request Body: None

#### Update Game
**PUT** `/api/v1/admin/games/{id}`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `id` (string, required): Game ID
- Request Body (JSON):

  | Field | Type   | Required | Description              |
  | ----- | ------ | -------- | ------------------------ |
  | pgn   | string | yes      | New PGN data to update   |
- Query Parameters: None
- **Response Body (JSON):** Updated game object including decompressed PGN in the `pgn` field.

### Puzzles Management

#### Create Puzzle
**POST** `/api/v1/admin/puzzles`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON): fields matching `CreatePuzzleRequest`:

  | Field        | Type             | Required | Description                       |
  | ------------ | ---------------- | -------- | --------------------------------- |
  | game_id      | string           | yes      | Related game ID                   |
  | user_id      | string           | yes      | Owning user ID                    |
  | game_move    | integer          | yes      | Move number in game               |
  | fen          | string           | yes      | FEN string for position           |
  | moves        | []string         | yes      | List of moves                     |
  | prev_cp      | integer          | yes      | Previous centipawn evaluation     |
  | cp           | integer          | yes      | Current centipawn evaluation      |
  | theme        | string           | yes      | Puzzle theme enum                 |
  | user_color   | string           | yes      | Color enum for user to move       |
  | puzzle_color | string           | yes      | Color enum for puzzle side        |
  | zugzwang     | boolean          | yes      | Zugzwang flag                     |
  | tags         | []string         | yes      | Tags for puzzle                   |

- Query Parameters: None

#### List Puzzles
**GET** `/api/v1/admin/puzzles`

- Path Parameters: None
- Query Parameters:
  | Parameter         | Type     | Required | Description                                    |
  | ----------------- | -------- | -------- | ---------------------------------------------- |
  | user_id           | string   | yes      | User ID to filter puzzles                      |
  | offset            | int      | no       | Pagination offset (default: 0)                 |
  | limit             | int      | no       | Pagination limit (1–100, default: 50)          |
  | tags              | string   | no       | Comma-separated tags filter (OR relation)      |
  | game_start_time   | RFC3339  | no       | Filter puzzles from game start timestamp       |
  | game_end_time     | RFC3339  | no       | Filter puzzles up to game end timestamp        |
- Request Body: None

#### Delete Puzzle
**DELETE** `/api/v1/admin/puzzles/{id}`

- Path Parameters:
  - `id` (string, required): Puzzle ID
- Query Parameters: None
- Request Body: None

### Task Object

Represents a background task in the system.

| Field        | Type       | Required | Description                                                                 |
|--------------|------------|----------|-----------------------------------------------------------------------------|
| id           | string     | yes      | UUID of the task                                                            |
| user_id      | string     | yes      | Owning user ID                                                              |
| task_type    | string     | yes      | One of `FetchChessGames`, `EvaluateChessGame`, `GenerateChessPuzzles`, `DeleteChessGames` |
| task_data    | object     | yes      | Task-specific JSON payload (see formats below)                              |
| status       | string     | yes      | One of `pending`, `in_progress`, `completed`, `failed`                      |
| error        | string/null| no       | Error message if status is `failed`                                         |
| attempts     | integer    | yes      | Number of processing attempts                                               |
| worker_id    | string/null| no       | ID of the worker that claimed the task                                      |
| picked_up_at | string/null| no       | RFC3339 timestamp when task was picked up                                    |
| scheduled_at | string     | yes      | RFC3339 timestamp when the task should become available for pickup (defaults to created_at) |
| created_at   | string     | yes      | RFC3339 timestamp when task was created                                      |
| updated_at   | string     | yes      | RFC3339 timestamp when task was last updated                                 |

#### Task Data Formats

Depending on the `task_type`, `task_data` must match one of:

- **FetchChessGamesData**:
  ```json
  {
    "user_id": "string",
    "chess_profile_id": "string"
  }
  ```
- **EvaluateChessGameData**:
  ```json
  {
    "user_id": "string",
    "game_id": "string"
  }
  ```
- **GenerateChessPuzzlesData**:
  ```json
  {
    "user_id": "string",
    "game_id": "string"
  }
  ```
- **DeleteChessGamesData**:
  ```json
  {
    "user_id": "string",
    "chess_profile_id": "string"
  }
  ```

### Tasks Management

#### Create Task
**POST** `/api/v1/admin/tasks`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON): fields matching `CreateTaskRequest`:

  | Field     | Type   | Required | Description                 |
  | --------- | ------ | -------- | --------------------------- |
  | user_id   | string | yes      | Owning user ID              |
  | task_type | string | yes      | Task type enum              |
  | task_data | object | yes      | Task-specific payload (JSON)|
  | scheduled_at | string | no       | RFC3339 timestamp (defaults to now/creation time) |

- Query Parameters: None

#### List Tasks
**GET** `/api/v1/admin/tasks`

- Path Parameters: None
- Query Parameters:
  | Parameter | Type   | Required | Description                          |
  | --------- | ------ | -------- | ------------------------------------ |
  | offset    | int    | no       | Pagination offset (default: 0)       |
  | limit     | int    | no       | Pagination limit (1–100, default: 50)|
  | status    | string | no       | Filter by task status enum           |
  | user_id   | string | no       | Filter tasks by user ID              |
- Request Body: None

#### Get Task By ID
**GET** `/api/v1/admin/tasks/{id}`

- Path Parameters:
  - `id` (string, required): Task ID
- Query Parameters: None
- Request Body: None

#### Claim Next Task
**POST** `/api/v1/admin/tasks/claim`

- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):

  | Field     | Type   | Required | Description        |
  | --------- | ------ | -------- | ------------------ |
  | worker_id | string | yes      | ID of claiming worker |

- Query Parameters: None

#### Update Task
**PUT** `/api/v1/admin/tasks/{id}`

- Headers:
  - `Content-Type: application/json`
- Path Parameters:
  - `id` (string, required): Task ID
- Request Body (JSON):

  | Field  | Type   | Required | Description                            |
  | ------ | ------ | -------- | -------------------------------------- |
  | status | string | no       | New task status enum                   |
  | error  | string | no       | Error message (empty to clear)         |

- Query Parameters: None

#### Clean Up Old Tasks
**POST** `/api/v1/admin/tasks/cleanup`

- Query Parameters:
  | Parameter | Type     | Required | Description                                          |
  | --------- | -------- | -------- | ---------------------------------------------------- |
  | cutoff    | RFC3339  | yes      | Cutoff timestamp for deletion (RFC3339)              |
  | statuses  | string   | no       | Comma-separated status enums (default: completed,failed) |
  | limit     | int      | no       | Max number of tasks to delete                        |
- Headers: None
- Request Body: None

#### Reset Hanging Tasks
**POST** `/api/v1/admin/tasks/reset-hanging`

- Query Parameters:
  | Parameter | Type     | Required | Description                                                 |
  | --------- | -------- | -------- | ----------------------------------------------------------- |
  | timeout   | string   | yes      | Duration string (e.g., "1h", "30m") to identify hanging tasks |
- Headers: None
- Request Body: None

### Sprint Management

#### Cleanup Abandoned Sprints
**POST** `/api/v1/admin/sprints/cleanup`

- Query Parameters:
  | Parameter     | Type | Required | Description                                          |
  | ------------- | ---- | -------- | ---------------------------------------------------- |
  | grace_minutes | int  | no       | Grace period in minutes before marking as abandoned (1–60, default: 5) |
  | limit         | int  | no       | Maximum number of sprints to process (1–10000, default: 1000) |
- Headers: None
- Request Body: None

---
## GraphQL API

The GraphQL API provides a flexible way to query user games and puzzles with filtering, pagination, and sorting options.

### GraphQL Endpoint
**POST** `/api/v1/graphql/query`

- Authentication: Bearer token
- Headers:
  - `Content-Type: application/json`
- Request Body (JSON):
  ```json
  {
    "query": "GraphQL query string",
    "variables": {} // Optional variables
  }
  ```

### GraphQL Schema Overview

The GraphQL schema provides the following main query types:

- **myGames**: Query your games with filtering, pagination, and sorting
- **myPuzzles**: Query your puzzles with filtering, pagination, and sorting
- **myGameStats**: Get aggregated statistics about your games
- **myPuzzleStats**: Get aggregated statistics about your puzzles
- **myGroupedGameStats**: Get time-grouped statistics about your games
- **myGroupedPuzzleStats**: Get time-grouped statistics about your puzzles
- **game**: Get a specific game by ID
- **puzzle**: Get a specific puzzle by ID

Each query supports various filters and pagination options. You can also filter puzzles within games using the `puzzles(filter: {...})` field on Game objects. For detailed examples, see the [GRAPHQL_EXAMPLES.md](docs/GRAPHQL_EXAMPLES.md) file.

### Key Features

- **Connection-based pagination**: All list queries return a connection object with edges, nodes, and page info
- **Rich filtering**: Filter games and puzzles by various criteria
- **Nested queries**: Query puzzles within games or games from puzzles
- **Sorting**: Sort results by various fields
- **PGN format**: Game PGN is automatically decompressed and returned in the response when querying games directly. Note that PGN is only accessible from the `myGames` query and the `game` query - it is not available when accessing a Game object through a Puzzle's `game` field to optimize performance.


### Example Queries

#### Query My Games
```graphql
{
  myGames(
    filter: {
      platform: CHESS_COM,
      start_time: "2023-01-01T00:00:00Z",
      user_color: WHITE
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    sort: {
      field: "GAME_TIME",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        platform
        chess_username
        pgn
        game_time
        time_control
        rated
        result
        user_color
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

#### Query My Puzzles
```graphql
{
  myPuzzles(
    filter: {
      tags: ["tactical", "endgame"], // OR relation between tags
      theme: OPPONENT_MISTAKE_CAUGHT,
      user_color: WHITE,
      puzzle_color: BLACK,
      game_move_min: 10,
      game_move_max: 40,
      prev_cp_min: -100,
      prev_cp_max: 100,
      cp_change_min: 200,
      cp_change_max: 500
    },
    pagination: {
      offset: 0,
      limit: 10
    },
    sort: {
      field: "CREATED_AT",
      direction: DESC
    }
  ) {
    edges {
      node {
        id
        fen
        moves
        theme
        tags
        user_color
        puzzle_color
        game_move
        prev_cp
        cp
      }
      cursor
    }
    page_info {
      has_next_page
      has_previous_page
    }
    total_count
  }
}
```

#### Query My Game Statistics
```graphql
{
  myGameStats(
    filter: {
      platform: CHESS_COM,
      start_time: "2023-01-01T00:00:00Z",
      rated: true
    }
  ) {
    platform_counts {
      platform
      count
    }
    user_color_counts {
      color
      count
    }
    result_counts {
      result
      count
    }
    time_control_counts {
      time_control
      count
    }
    rated_counts {
      rated
      count
    }
    average_opponent_rating
    total_count
  }
}
```

#### Query My Puzzle Statistics
```graphql
{
  myPuzzleStats(
    filter: {
      themes: [OPPONENT_MISTAKE_MISSED],
      user_color: WHITE,
      game_start_time: "2023-01-01T00:00:00Z"
    }
  ) {
    tag_counts {
      tag
      count
    }
    theme_counts {
      theme
      count
    }
    user_color_counts {
      color
      count
    }
    game_move_buckets {
      name
      min_move
      max_move
      count
    }
    move_length_counts {
      length
      count
    }
    total_count
    unique_game_count
    average_move_length
  }
}
```

#### Query Grouped Game Statistics
```graphql
{
  myGroupedGameStats(
    filter: {
      start_time: "2023-01-01T00:00:00Z",
      end_time: "2023-12-31T23:59:59Z"
    },
    group_unit: MONTH,
    group_length: 1
  ) {
    nodes {
      start_time
      end_time
      stats {
        platform_counts {
          platform
          count
        }
        average_opponent_rating
        total_count
      }
    }
    total_count
  }
}
```

#### Query Grouped Puzzle Statistics
```graphql
{
  myGroupedPuzzleStats(
    filter: {
      themes: [OPPONENT_MISTAKE_MISSED],
      game_start_time: "2023-01-01T00:00:00Z",
      game_end_time: "2023-12-31T23:59:59Z"
    },
    group_unit: WEEK,
    group_length: 2
  ) {
    nodes {
      start_time
      end_time
      stats {
        theme_counts {
          theme
          count
        }
        average_move_length
        unique_game_count
        total_count
      }
    }
    total_count
  }
}
```

### Multiple Themes Filtering

The `themes` field in `PuzzleFilter` allows filtering by multiple themes using OR logic. This is particularly useful for excluding uninteresting themes or combining related themes.

#### Example: Exclude "Caught" Themes (Focus on Improvement Areas)
```graphql
{
  myPuzzles(
    filter: {
      themes: [
        OPPONENT_BLUNDER_MISSED,
        OPPONENT_MISTAKE_MISSED,
        OWN_BLUNDER_PUNISHED,
        OWN_MISTAKE_PUNISHED
      ]
    },
    pagination: { limit: 100 }
  ) {
    edges {
      node {
        id
        theme
        game {
          game_time
        }
      }
    }
    total_count
  }
}
```

#### Example: Focus on Specific Theme Categories
```graphql
{
  myPuzzleStats(
    filter: {
      themes: [OPPONENT_BLUNDER_MISSED, OPPONENT_MISTAKE_MISSED],
      game_start_time: "2023-01-01T00:00:00Z"
    }
  ) {
    theme_counts {
      theme
      count
    }
    total_count
    average_move_length
  }
}
```

#### Example: Compare Multiple Theme Groups
```graphql
{
  # Missed opportunities
  missedOpportunities: myPuzzleStats(
    filter: {
      themes: [OPPONENT_BLUNDER_MISSED, OPPONENT_MISTAKE_MISSED]
    }
  ) {
    total_count
    average_move_length
  }

  # Own mistakes
  ownMistakes: myPuzzleStats(
    filter: {
      themes: [OWN_BLUNDER_PUNISHED, OWN_MISTAKE_PUNISHED]
    }
  ) {
    total_count
    average_move_length
  }
}
```

### Rate Limiting

The GraphQL API is rate limited to prevent server overload. The current limit is 10 requests per second with a burst of 30 requests.

---
# Example Responses

## Health Check
**GET** `/health`

**Response**:
```
OK
```

## Authentication

Chessticize uses Firebase Authentication. To obtain a Chessticize API token, authenticate with Firebase (Email/Password, Magic Link, Google) in the client and call the Firebase Token Exchange endpoint to receive a short-lived API token.

## Get Current User
**GET** `/api/v1/users/me`

**Response**:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "registered_at": "2023-10-01T12:00:00Z",
  "updated_at": "2023-10-01T12:00:00Z",
  "last_sign_in_at": "2023-10-02T08:30:00Z",
  "chess_profiles": [],
  "daily_stats": [
    {
      "date": "2023-10-02",
      "puzzle_success": 5,
      "puzzle_total": 8,
      "streak": 3
    }
  ],
  "sprint_daily_stats": [
    {
      "date": "2023-10-02",
      "elo_type": "blitz",
      "sprint_success": 2,
      "sprint_total": 3,
      "sprint_total_duration": 1200,
      "puzzles_solved": 42,
      "puzzles_attempted": 55,
      "streak": 4
    }
  ]
}
```

## List My Chess Profiles
**GET** `/api/v1/users/me/chess-profiles`

**Response**:
```json
[
  {
    "id": "profile-id-1",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "platform": "lichess.org",
    "username": "chessplayer",
    "games_fetched": 10,
    "last_game_fetched_at": "2023-10-05T14:00:00Z",
    "last_game_played_at": "2023-10-05T13:45:00Z",
    "updated_at": "2023-10-05T14:00:00Z"
  }
]
```

## Create My Chess Profile
**POST** `/api/v1/users/me/chess-profiles`

**Response** (201 Created):
```json
{
  "id": "new-profile-id",
  "user_id": "123e4567-e89b-12d3-a456-************",
  "platform": "lichess.org",
  "username": "newuser",
  "games_fetched": 0,
  "last_game_fetched_at": null,
  "last_game_played_at": null,
  "updated_at": "2023-10-06T10:00:00Z"
}
```

## Delete My Chess Profile
**DELETE** `/api/v1/users/me/chess-profiles/{profileID}`

**Response**:
```
204 No Content
```

## List My Games
**GET** `/api/v1/users/me/games`

**Response**:
```json
{
  "games": [ /* array of game objects */ ],
  "total_count": 5,
  "offset": 0,
  "limit": 50
}
```

## List My Puzzles
**GET** `/api/v1/users/me/puzzles`

**Response**:
```json
{
  "puzzles": [ /* array of puzzle objects */ ],
  "total_count": 3,
  "offset": 0,
  "limit": 50
}
```

## List Users (Admin)
**GET** `/api/v1/admin/users`

**Response**:
```json
[ /* array of user objects */ ]
```

## Create User (Admin)
**POST** `/api/v1/admin/users`

**Response** (201 Created):
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174001",
  "email": "<EMAIL>",
  "registered_at": "2023-10-07T09:00:00Z",
  "updated_at": "2023-10-07T09:00:00Z",
  "last_sign_in_at": null
}
```

## Get User By ID (Admin)
**GET** `/api/v1/admin/users/{id}`

**Response**:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "registered_at": "2023-10-01T12:00:00Z",
  "updated_at": "2023-10-01T12:00:00Z",
  "last_sign_in_at": "2023-10-02T08:30:00Z",
  "chess_profiles": []
}
```

## Update User (Admin)
**PUT** `/api/v1/admin/users/{id}`

**Response**:
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "registered_at": "2023-10-01T12:00:00Z",
  "updated_at": "2023-10-08T11:00:00Z",
  "last_sign_in_at": "2023-10-02T08:30:00Z",
  "chess_profiles": []
}
```

## List User's Chess Profiles (Admin)
**GET** `/api/v1/admin/users/{id}/chess-profiles`

**Response**:
```json
[ /* array of chess profile objects */ ]
```

## Refresh Chess Profile (Admin)
**PUT** `/api/v1/admin/users/{id}/chess-profiles/{profileID}/refresh`

**Response**:
```json
{ /* updated chess profile object */ }
```

## Create Game (Admin)
**POST** `/api/v1/admin/games`

**Response** (201 Created):
```json
{ /* created game object */ }
```

## List Games By User (Admin)
**GET** `/api/v1/admin/games/user/{userId}`

**Response**:
```json
{
  "games": [ /* array of game objects */ ],
  "total_count": 2,
  "offset": 0,
  "limit": 50
}
```

## Delete Game (Admin)
**DELETE** `/api/v1/admin/games/{id}`

**Response**:
```
200 OK
```

## Get Game By ID (Admin)
**GET** `/api/v1/admin/games/{id}`

**Response**:
```json
{ /* game object */ }
```

## Update Game (Admin)
**PUT** `/api/v1/admin/games/{id}`

**Response**:
```json
{ /* updated game object */ }
```

## Create Puzzle (Admin)
**POST** `/api/v1/admin/puzzles`

**Response** (201 Created):
```json
{ /* created puzzle object */ }
```

## List Puzzles (Admin)
**GET** `/api/v1/admin/puzzles`

**Response**:
```json
{
  "puzzles": [ /* array of puzzle objects */ ],
  "total_count": 4,
  "offset": 0,
  "limit": 50
}
```

## Delete Puzzle (Admin)
**DELETE** `/api/v1/admin/puzzles/{id}`

**Response**:
```
200 OK
```

## Create Task (Admin)
**POST** `/api/v1/admin/tasks`

**Response** (201 Created):
```json
{ /* created task object */ }
```

## List Tasks (Admin)
**GET** `/api/v1/admin/tasks`

**Response**:
```json
{
  "tasks": [ /* array of task objects */ ],
  "total_count": 5,
  "offset": 0,
  "limit": 50
}
```

## Get Task By ID (Admin)
**GET** `/api/v1/admin/tasks/{id}`

**Response**:
```json
{ /* task object */ }
```

## Claim Next Task (Admin)
**POST** `/api/v1/admin/tasks/claim`

**Response**:
```json
{ /* claimed task object or error message if none */ }
```

## Update Task (Admin)
**PUT** `/api/v1/admin/tasks/{id}`

**Response**:
```json
{ /* updated task object */ }
```

## Clean Up Old Tasks (Admin)
**POST** `/api/v1/admin/tasks/cleanup`

**Response**:
```json
{
  "deleted_count": 10
}
```

## Reset Hanging Tasks (Admin)
**POST** `/api/v1/admin/tasks/reset-hanging`

**Response**:
```json
{
  "reset_count": 3
}
```

## Start Sprint
**POST** `/api/v1/users/me/sprint/start`

**Response** (201 Created):
```json
{
  "session_id": "123e4567-e89b-12d3-a456-************",
  "user_elo": {
    "rating": 1500,
    "rating_deviation": 200.0,
    "is_provisional": false
  },
  "target_puzzles": 20,
  "time_limit_seconds": 600,
  "max_mistakes": 2,
  "attempt_type": "arrow_duel"
}
```

**Response Fields:**
| Field              | Type   | Always Present | Description                                   |
| ------------------ | ------ | -------------- | --------------------------------------------- |
| session_id         | string | yes            | Unique session identifier for this sprint    |
| user_elo           | object | yes            | User's current ELO rating information         |
| target_puzzles     | int    | yes            | Number of puzzles to solve for success       |
| time_limit_seconds | int    | yes            | Time limit for the sprint in seconds         |
| max_mistakes       | int    | yes            | Maximum allowed mistakes before failure       |
| attempt_type       | string | no             | "regular" or "arrow_duel" based on elo_type  |

**Note**: For arrow-duel sprints (elo_type contains "arrowduel"), the `attempt_type` field will be "arrow_duel". For regular sprints, this field may be omitted or set to "regular".

## Get Sprint State
**GET** `/api/v1/users/me/sprint/{sessionId}`

**Response**:
```json
{
  "session_id": "123e4567-e89b-12d3-a456-************",
  "status": "ACTIVE",
  "puzzles_solved": 5,
  "mistakes_made": 1,
  "max_mistakes": 2,
  "target_puzzles": 20,
  "started_at": "2023-10-01T12:00:00Z",
  "time_remaining_seconds": 450
}
```

## End Sprint
**POST** `/api/v1/users/me/sprint/{sessionId}/end`

Ends a sprint session and calculates final results. Optionally accepts client-provided final counts to override server calculations.

**Request Body (Optional)**:
```json
{
  "puzzles_solved": 15,
  "mistakes_made": 1
}
```

**Request Fields**:
| Field           | Type | Required | Description                                    |
| --------------- | ---- | -------- | ---------------------------------------------- |
| puzzles_solved  | int  | no       | Client's count of puzzles solved (≥ 0)        |
| mistakes_made   | int  | no       | Client's count of mistakes made (≥ 0)         |

**Response**:
```json
{
  "session_id": "123e4567-e89b-12d3-a456-************",
  "status": "COMPLETED_SUCCESS",
  "puzzles_solved": 20,
  "mistakes_made": 1,
  "max_mistakes": 2,
  "target_puzzles": 20,
  "duration_seconds": 580,
  "elo_change": {
    "rating_before": 1500,
    "rating_after": 1520,
    "rating_change": 20
  }
}
```

**Response Fields**:
| Field             | Type   | Description                                    |
| ----------------- | ------ | ---------------------------------------------- |
| session_id        | string | Sprint session ID                              |
| status            | string | Final sprint status (see Sprint Status values) |
| puzzles_solved    | int    | Final count of puzzles solved                  |
| mistakes_made     | int    | Final count of mistakes made                   |
| max_mistakes      | int    | Maximum allowed mistakes for this sprint      |
| target_puzzles    | int    | Target number of puzzles for this sprint      |
| duration_seconds  | int    | Total sprint duration in seconds              |
| elo_change        | object | ELO rating change details (if available)      |

**Sprint Status Values**:
- `COMPLETED_SUCCESS`: Sprint completed successfully (puzzles_solved ≥ target_puzzles AND mistakes_made ≤ max_mistakes)
- `COMPLETED_FAIL_MISTAKES`: Sprint failed due to too many mistakes (mistakes_made > max_mistakes)
- `COMPLETED_FAIL_TIME`: Sprint failed due to timeout (puzzles_solved < target_puzzles AND mistakes_made ≤ max_mistakes)

**Use Cases for Client-Provided Results**:
- **Result Drift Mitigation**: When client-side tracking differs from server-side due to network issues
- **Missed Submissions**: When some puzzle results failed to reach the server
- **Duplicate Prevention**: Combined with server-side deduplication to ensure accurate counts

## Get Next Puzzles for Sprint
**POST** `/api/v1/users/me/sprint/{sessionId}/next-puzzles`

**Response**:

**Regular Sprint Response:**
```json
{
  "puzzles": [
    {
      "puzzle_id": "puzzle123",
      "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "solution_moves": ["e2e4", "e7e5"],
      "rating": 1500,
      "themes": ["opening", "tactics"],
      "sequence_in_sprint": 1,
      "attempt_type": "regular"
    }
  ]
}
```

**Arrow-Duel Sprint Response:**
```json
{
  "puzzles": [
    {
      "puzzle_id": "puzzle123",
      "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "solution_moves": ["e2e4", "e7e5"],
      "rating": 1500,
      "themes": ["opening", "tactics"],
      "sequence_in_sprint": 1,
      "attempt_type": "arrow_duel",
      "best_move_eval": 45.5,
      "best_move": "e2e4",
      "position_eval_after_first_move": -280.0
    }
  ]
}
```

**Response Fields**:
| Field                          | Type     | Required | Description                                    |
| ------------------------------ | -------- | -------- | ---------------------------------------------- |
| puzzle_id                      | string   | yes      | Unique identifier for the puzzle               |
| fen                           | string   | yes      | FEN notation of the puzzle position            |
| solution_moves                | []string | yes      | Array of moves in the puzzle solution          |
| rating                        | int      | yes      | Puzzle difficulty rating                       |
| themes                        | []string | yes      | Array of puzzle themes/tags                    |
| sequence_in_sprint            | int      | yes      | Position of this puzzle in the sprint         |
| attempt_type                  | string   | yes      | Type of attempt: "regular" or "arrow_duel"     |
| best_move_eval                | float64  | no*      | Stockfish evaluation of the best move in centipawns from white's perspective |
| best_move                     | string   | no*      | Best move in algebraic notation (e.g., "e2e4") |
| position_eval_after_first_move| float64  | no*      | Stockfish evaluation after the first move in the puzzle solution from white's perspective |

*Only included for arrow-duel sprints (`attempt_type: "arrow_duel"`)

## Submit Sprint Puzzle Results
**POST** `/api/v1/users/me/sprint/{sessionId}/results`

Submits puzzle attempt results for a sprint session. Includes automatic deduplication to prevent double-counting.

**Response**:
```json
{
  "processed_count": 2,
  "session_status": "ACTIVE",
  "mistakes_count": 1
}
```

**Response Fields**:
| Field           | Type   | Description                                    |
| --------------- | ------ | ---------------------------------------------- |
| processed_count | int    | Number of new results processed (excludes duplicates) |
| session_status  | string | Current sprint status (`ACTIVE` or completed status) |
| mistakes_count  | int    | Current total number of mistakes in the sprint |

**Notes**:
- `processed_count` may be less than the number of results submitted if duplicates are detected
- Duplicate submissions (same puzzle in same sprint) are automatically ignored
- Sprint status remains `ACTIVE` until explicitly ended via the End Sprint endpoint

## Get Random Lichess Puzzles
**POST** `/api/v1/users/me/random-puzzles/lichess`

**Response**:
```json
{
  "puzzles": [
    {
      "id": "lichess_puzzle_123",
      "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "moves": ["e2e4", "e7e5"],
      "rating": 1500,
      "themes": ["opening", "tactics"]
    }
  ],
  "count": 1
}
```

## Get Random User Puzzles
**POST** `/api/v1/users/me/random-puzzles/user`

**Response**:
```json
{
  "puzzles": [
    {
      "id": "user_puzzle_123",
      "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "moves": ["e2e4", "e7e5"],
      "move_length": 2,
      "tags": ["tactical", "endgame"],
      "theme": "OPPONENT_MISTAKE_CAUGHT",
      "user_color": "WHITE",
      "puzzle_color": "BLACK"
    }
  ],
  "count": 1
}
```

## Submit Puzzle Attempt
**POST** `/api/v1/users/me/puzzles/{puzzleID}/attempts`

**Response** (Regular and Arrow-Duel):
```json
{
  "success": true,
  "message": "Puzzle attempt recorded successfully"
}
```

**Note**: Both regular and arrow-duel attempts return the same response format. The attempt type is determined by the request body and affects internal processing and statistics tracking.

## Submit Lichess Puzzle Attempt
**POST** `/api/v1/users/me/lichess-puzzles/{puzzleID}/attempts`

**Response** (Regular and Arrow-Duel):
```json
{
  "success": true,
  "message": "Lichess puzzle attempt recorded successfully"
}
```

**Note**: Both regular and arrow-duel attempts return the same response format. The attempt type is determined by the request body and affects internal processing and statistics tracking.

## List My Events
**GET** `/api/v1/users/me/events`

**Response**:
```json
{
  "events": [
    {
      "id": "event123",
      "user_id": "123e4567-e89b-12d3-a456-************",
      "event_type": "PUZZLE_ATTEMPT",
      "event_data": {
        "puzzle_id": "puzzle123",
        "was_correct": true,
        "time_taken_ms": 5000
      },
      "created_at": "2023-10-01T12:00:00Z"
    }
  ],
  "total_count": 1,
  "offset": 0,
  "limit": 50
}
```

## Add Puzzles to Queue
**POST** `/api/v1/users/me/puzzle-queue/add`

**Response**:
```json
{
  "added_count": 15,
  "skipped_count": 5,
  "message": "Puzzles added to queue successfully"
}
```

## Get Due Puzzles
**GET** `/api/v1/users/me/puzzle-queue/due`

**Response**:
```json
{
  "puzzles": [
    {
      "id": "queue-entry-123",
      "user_id": "123e4567-e89b-12d3-a456-************",
      "puzzle_id": "puzzle123",
      "puzzle_theme": "OPPONENT_MISTAKE_MISSED",
      "mistake_by": "opponent",
      "next_due": "2023-10-01T12:00:00Z",
      "interval_days": 2,
      "consecutive_successes": 1,
      "created_at": "2023-09-29T12:00:00Z",
      "updated_at": "2023-09-30T12:00:00Z"
    }
  ],
  "total_due": 5
}
```

## Get Queue Statistics
**GET** `/api/v1/users/me/puzzle-queue/stats`

**Response**:
```json
{
  "total_puzzles": 50,
  "due_today": 5,
  "overdue": 2,
  "completed": 10
}
```

## Get Due Learning Puzzles
**GET** `/api/v1/users/me/learning-queue/due`

**Response**:
```json
{
  "puzzles": [
    {
      "queue_id": "123e4567-e89b-12d3-a456-************",
      "lichess_puzzle_id": "00008",
      "failed_attempt_type": "arrow_duel",
      "due_at": "2024-01-15T10:00:00Z",
      "attempts_since_added": 1,
      "consecutive_correct": 0,
      "original_sprint_id": "sprint-uuid-123",
      "puzzle_data": {
        "id": "00008",
        "fen": "r6k/pp2r2p/4Rp1Q/3p4/8/1N1P2R1/PqP2bPP/7K b - - 0 24",
        "moves": ["f2g3", "e6e7", "b2b1", "b3c1", "b1c1", "h6c1"],
        "rating": 1798,
        "themes": ["crushing", "hangingPiece", "long", "middlegame"]
      }
    }
  ],
  "total_due": 15
}
```

## Get Learning Queue Statistics
**GET** `/api/v1/users/me/learning-queue/stats`

**Response**:
```json
{
  "total_queued": 25,
  "due_today": 8,
  "regular_puzzles": 15,
  "arrow_duel_puzzles": 10,
  "daily_retries_today": 5,
  "daily_retries_this_week": 23,
  "mastery_rate": 0.65
}
```

## Cleanup Abandoned Sprints (Admin)
**POST** `/api/v1/admin/sprints/cleanup`

**Response**:
```json
{
  "abandoned_count": 15,
  "message": "Successfully marked 15 sprints as abandoned"
}
```