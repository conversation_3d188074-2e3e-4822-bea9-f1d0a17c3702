{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NEXT_PUBLIC_USE_FIREBASE_EMULATOR=true next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:arrow-duel": "./scripts/test-arrow-duel.sh", "test:smoke": "vitest __tests__/smoke/", "test:integration": "vitest __tests__/components/puzzle-sprint/ArrowDuelIntegration.test.tsx", "emulator": "firebase emulators:start --only auth", "emulator:export": "firebase emulators:export ./emulator-data", "emulator:import": "firebase emulators:start --import=./emulator-data"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "chess.js": "^1.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "firebase": "^12.0.0", "immer": "^9.0.21", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "rdndmb-html5-to-touch": "^9.0.0", "react": "^19", "react-chessboard": "^4.7.3", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-multi-backend": "^9.0.0", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "stockfish": "^16.0.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chess.js": "^0.13.7", "@types/js-cookie": "^3.0.6", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.0", "@vitest/ui": "^3.1.4", "cross-env": "^10.0.0", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "firebase-tools": "^14.11.1", "jsdom": "^26.1.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^3.1.4"}}