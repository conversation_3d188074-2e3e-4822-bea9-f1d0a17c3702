/**
 * TypeScript types for authentication
 */

// Login request types
export interface LoginWithCredentials {
  email: string
  password: string
}

export interface LoginWithSessionToken {
  session_token: string
}

export type LoginRequest = LoginWithCredentials | LoginWithSessionToken

// Registration request
export interface RegisterRequest {
  email: string
  password: string
  invitation_code?: string // Optional for backward compatibility
}

// Authentication responses
export interface LoginResponse {
  token: string
  session_token?: string // Only present for email/password login
}

export interface RegisterResponse {
  id: string
  email: string
  token: string
  session_token: string
}

// Daily stats types
export interface UserDailyStats {
  date: string
  puzzle_success: number
  puzzle_total: number
  streak: number
}

export interface UserSprintDailyStats {
  date: string
  elo_type: string
  sprint_success: number
  sprint_total: number
  sprint_total_duration: number
  puzzles_solved: number
  puzzles_attempted: number
  streak: number
}

export interface UserElo {
  elo_type: string
  rating: number
  rating_deviation: number
  is_provisional: boolean
  updated_at: string
}

// User types
export interface User {
  id: string
  email: string
  registered_at: string
  updated_at: string
  last_sign_in_at: string
  daily_stats?: UserDailyStats[]
  sprint_daily_stats?: UserSprintDailyStats[]
  elos?: UserElo[]
}



// Session token types
export interface SessionToken {
  id: string
  user_agent: string
  expires_at: string
  created_at: string
  updated_at: string
}

// Authentication state
export interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
  error: string | null
}

// Token storage
export interface TokenData {
  authToken: string | null
  sessionToken: string | null
  expiryTime: Date | null
}

// API error types
export interface ApiError {
  message: string
  status?: number
  code?: string
}

// Chess profile creation
export interface CreateChessProfileRequest {
  platform: string
  username: string
}

export interface CreateChessProfileResponse {
  id: string
  user_id: string
  platform: string
  username: string
  games_fetched: number
  last_game_fetched_at: string | null
  last_game_played_at: string | null
  updated_at: string
}
