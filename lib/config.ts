/**
 * Centralized application configuration
 * Adapts the legacy site's API configuration for the new Next.js app
 */

import { env } from './env'

export const API_CONFIG = {
  // Base URL for all API requests
  BASE_URL: `${env.API_BASE_URL}/api/v1`,
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Default headers for all requests
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
  
  // Authentication endpoints
  ENDPOINTS: {
    FIREBASE_EXCHANGE: '/auth/firebase-exchange',
    USER_ME: '/users/me',
    GAMES: '/users/me/games',
    PUZZLES: '/users/me/puzzles',
    GRAPHQL: '/graphql/query',
  },
} as const

// Derived endpoints
export const GRAPHQL_ENDPOINT = `${API_CONFIG.BASE_URL}/graphql/query`

// Token configuration
export const TOKEN_CONFIG = {
  // Cookie names
  AUTH_TOKEN_COOKIE: 'auth_token',
  SESSION_TOKEN_COOKIE: 'session_token',
  TOKEN_EXPIRY_COOKIE: 'token_expiry',
  
  // Token refresh buffer (5 minutes in milliseconds)
  REFRESH_BUFFER: 5 * 60 * 1000,
  
  // Default token expiry (60 minutes)
  DEFAULT_EXPIRY_MINUTES: 60,
  
  // Cookie options
  COOKIE_OPTIONS: {
    expires: 7, // 7 days
    secure: env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
  },
  
  // Session token cookie options (longer expiry)
  SESSION_COOKIE_OPTIONS: {
    expires: 30, // 30 days
    secure: env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
  },
} as const

// Application configuration
export const APP_CONFIG = {
  // Application name
  NAME: 'Chessticize',
  
  // Default redirect paths
  DEFAULT_LOGIN_REDIRECT: '/puzzle-sprint',
  DEFAULT_LOGOUT_REDIRECT: '/auth',
} as const

// Debug configuration
export const DEBUG_CONFIG = {
  ENABLED: env.ENABLE_DEBUG_LOGS,
  PREFIX: '[Chessticize]',
} as const

// Helper function for debug logging
export function debugLog(...args: any[]) {
  if (DEBUG_CONFIG.ENABLED) {
    console.log(DEBUG_CONFIG.PREFIX, ...args)
  }
}
