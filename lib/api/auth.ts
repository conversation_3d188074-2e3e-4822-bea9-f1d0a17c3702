/**
 * Authentication API functions
 */

import { apiPost, apiGet, apiDelete, ApiError } from '../auth/api-client'
import { API_CONFIG } from '../config'
import {
  User
} from '../auth/types'

/**
 * Login with email/password or session token
 */


/**
 * Register a new user with invitation code
 */


/**
 * Get current user information
 */
export async function getCurrentUser(): Promise<User> {
  try {
    const response = await apiGet(API_CONFIG.ENDPOINTS.USER_ME)
    
    if (!response.ok) {
      throw new ApiError('Failed to get user information', response.status)
    }
    
    return await response.json()
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error getting user information')
  }
}

/**
 * Get user's session tokens
 */


/**
 * Revoke a session token
 */




/**
 * Delete a chess profile
 */
export async function deleteChessProfile(profileId: string): Promise<void> {
  try {
    const response = await apiDelete(`${API_CONFIG.ENDPOINTS.CHESS_PROFILES}/${profileId}`)
    
    if (!response.ok) {
      throw new ApiError('Failed to delete chess profile', response.status)
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    throw new ApiError('Network error deleting chess profile')
  }
}
